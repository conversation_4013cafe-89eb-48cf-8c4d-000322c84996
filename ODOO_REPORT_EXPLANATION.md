# Odoo Event Report Code Explanation - Step by Step

## Overview
This document explains the Odoo QWeb report templates for event management, specifically for passenger manifests, boarding passes, and registration invoices.

## 1. XML Structure and Namespaces

```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- All templates and records go here -->
</odoo>
```

The file starts with XML declaration and the main `<odoo>` tag that wraps all Odoo-specific content.

## 2. QWeb Templates Explained

### Template 1: Attendee List (Passenger Manifest)

```xml
<template id="event.attendee_list">
```

This template generates a passenger manifest report showing all attendees for an event.

#### Key Components:

**A. Header Section:**
```xml
<div class="row text-center">
    <h3>PASSENGER MANIFEST</h3>
</div>
```
- Creates a centered title for the report

**B. Event Information:**
```xml
<div class="col-4">
    <span><b>Vessel:</b> </span><br/>
    <span><b>Voyage From:</b> <t t-out="event.from_location_id.name"/></span><br/>
    <span><b>To:</b> <t t-out="event.to_location_id.name"/></span>
</div>
```
- `t-out`: QWeb directive to output field values
- `event.from_location_id.name`: Accesses the name of the departure location
- `event.to_location_id.name`: Accesses the name of the destination location

**C. Data Processing with t-set:**
```xml
<t t-set="adult" t-value="attendees.mapped('registration_answer_ids').filtered(lambda x: x.value_answer_id.name == 'ADULT')"/>
```

**Breakdown:**
- `t-set`: Creates a variable named "adult"
- `attendees.mapped('registration_answer_ids')`: Gets all registration answers from all attendees
- `.filtered(lambda x: x.value_answer_id.name == 'ADULT')`: Filters to only adult passengers
- This creates a list of adult passenger records

**D. Statistics Table:**
```xml
<table class="table table-bordered mt-3">
    <thead>
        <th>Count</th>
        <th>Adult</th>
        <th>Child</th>
        <!-- ... more columns -->
    </thead>
    <tbody>
        <tr>
            <td><t t-out="len(attendees)"/></td>
            <td><t t-out="len(adult)"/></td>
            <!-- ... more data -->
        </tr>
    </tbody>
</table>
```
- `len(attendees)`: Counts total number of attendees
- `len(adult)`: Counts number of adult passengers

## 3. Data Extraction Pattern

### Understanding registration_answer_ids

In Odoo events, passenger information is stored as questions and answers:

```xml
<t t-set="pax_name" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Pax Name')"/>
```

**How it works:**
1. `attendee.registration_answer_ids`: Gets all answers for this attendee
2. `.filtered(lambda x: ...)`: Filters to find specific question
3. `x.question_id.title == 'Pax Name'`: Matches question by title
4. Result: The answer record for the "Pax Name" question

### Accessing Answer Values

```xml
<td><t t-out="pax_name.value_text_box"/></td>  <!-- For text answers -->
<td><t t-out="ntly.value_answer_id.name"/></td>  <!-- For selection answers -->
```

- `value_text_box`: Used for text input answers
- `value_answer_id.name`: Used for selection/dropdown answers

## 4. Loop Processing

```xml
<t t-set="count" t-value="1"/>
<tr t-foreach="attendees" t-as="attendee">
    <!-- Process each attendee -->
    <td><t t-out="count"/></td>
    <!-- ... other data -->
    <t t-set="count" t-value="count+1"/>
</tr>
```

**Explanation:**
- `t-foreach="attendees"`: Loops through all attendees
- `t-as="attendee"`: Current item is named "attendee"
- `count` variable tracks row numbers
- `count+1`: Increments counter for next row

## 5. Template Inheritance (Your Enhancement)

```xml
<template id="inherit_event_attendee_list" inherit_id="event.attendee_list">
```

**Key Concepts:**
- `inherit_id`: Specifies which template to inherit from
- `xpath`: Used to locate and modify specific parts of the parent template

### XPath Examples:

**A. Adding Content After an Element:**
```xml
<xpath expr="//div[hasclass('row')][1]" position="after">
    <div class="row">
        <!-- New content here -->
    </div>
</xpath>
```

**B. Replacing Content:**
```xml
<xpath expr="//div[hasclass('col-4')][1]" position="replace">
    <div class="col-4">
        <!-- Replacement content -->
    </div>
</xpath>
```

## 6. Your Enhancements Explained

### A. Bilingual Headers
```xml
<th>الاسم الأول<br/>First Name</th>
```
- Uses `<br/>` to create two-line headers
- Arabic text on top, English below

### B. Flight Number Display
```xml
<span><b>رقم الرحلة / Flight No:</b> <t t-out="event.sequence or event.name"/></span>
```
- `event.sequence or event.name`: Shows sequence if available, otherwise shows name
- Based on your screenshots, this will show "TP031"

### C. Date and Time Display
```xml
<span><b>التاريخ / Date:</b> <t t-out="event.date_begin.strftime('%Y-%m-%d')"/></span>
```
- `strftime('%Y-%m-%d')`: Formats date as YYYY-MM-DD
- `strftime('%H:%M')`: Formats time as HH:MM

### D. Field Name Changes
```xml
<t t-set="first_name" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'First Name')"/>
<t t-set="surname" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Surname')"/>
```
- Changed from "Pax Name" to "First Name"
- Changed from "Father Name" to "Surname"
- Matches your question configuration

## 7. Report Configuration Records

```xml
<record id="paperformat_attendee_list" model="report.paperformat">
    <field name="name">Attendee List</field>
    <field name="format">A4</field>
    <field name="orientation">Landscape</field>
    <!-- ... more settings -->
</record>
```

**Purpose:**
- Defines paper size and orientation
- Sets margins and DPI
- Links to report actions

```xml
<record id="event.action_report_event_event_attendee_list" model="ir.actions.report">
    <field name="name">Attendee List</field>
    <field name="model">event.event</field>
    <field name="report_type">qweb-pdf</field>
    <field name="report_name">event.event_event_attendee_list</field>
    <!-- ... more settings -->
</record>
```

**Purpose:**
- Creates the report action that appears in menus
- Links template to data model
- Defines PDF generation settings

## 8. Key Odoo Concepts Used

### A. QWeb Directives
- `t-out`: Output field values
- `t-set`: Create variables
- `t-foreach`: Loop through records
- `t-as`: Name current loop item
- `t-if`: Conditional display

### B. Python Expressions in Templates
- `lambda x: x.condition`: Filter functions
- `.mapped()`: Extract field values from recordsets
- `.filtered()`: Filter recordsets
- `len()`: Count items
- `.strftime()`: Format dates

### C. Bootstrap CSS Classes
- `table table-bordered`: Styled table
- `row`, `col-4`: Grid system
- `text-center`: Center alignment
- `mt-3`: Margin top

This structure allows for flexible, data-driven reports that can be easily customized and inherited.

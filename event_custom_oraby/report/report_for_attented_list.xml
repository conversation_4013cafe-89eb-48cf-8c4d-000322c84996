<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="inherit_event_attendee_list" inherit_id="event.attendee_list">

        <!-- التاريخ والوقت مع تنسيق محسن -->
        <xpath expr="//div[hasclass('row')][1]" position="after">
            <div class="row" style="text-align: right; margin-bottom: 20px; font-size: 13px;">
                <div class="col-12">
                    <div style="display: inline-block; margin-right: 30px;">
                        <span style="font-weight: bold;">Date:</span> <t t-out="event.date_begin.strftime('%d/%m/%Y')"/><br/>
                        <span style="font-weight: bold; font-size: 12px;">التاريخ:</span> <t t-out="event.date_begin.strftime('%d/%m/%Y')"/>
                    </div>
                    <div style="display: inline-block;">
                        <span style="font-weight: bold;">Time:</span> <t t-out="event.date_begin.strftime('%H:%M')"/><br/>
                        <span style="font-weight: bold; font-size: 12px;">الوقت:</span> <t t-out="event.date_begin.strftime('%H:%M')"/>
                    </div>
                </div>
            </div>
        </xpath>

        <!-- رقم الرحلة بدلاً من Vessel مع تنسيق محسن -->
        <xpath expr="//div[hasclass('col-4')][1]" position="replace">
            <div class="col-4" style="margin-bottom: 20px; font-size: 13px;">
                <div style="margin-bottom: 8px;">
                    <span style="font-weight: bold;">Flight No:</span> <t t-out="event.sequence or event.name"/><br/>
                    <span style="font-weight: bold; font-size: 12px;">رقم الرحلة:</span> <t t-out="event.sequence or event.name"/>
                </div>
                <div style="margin-bottom: 8px;">
                    <span style="font-weight: bold;">Voyage From:</span> <t t-out="event.from_location_id.name"/><br/>
                    <span style="font-weight: bold; font-size: 12px;">من:</span> <t t-out="event.from_location_id.name"/>
                </div>
                <div>
                    <span style="font-weight: bold;">To:</span> <t t-out="event.to_location_id.name"/><br/>
                    <span style="font-weight: bold; font-size: 12px;">إلى:</span> <t t-out="event.to_location_id.name"/>
                </div>
            </div>
        </xpath>

        <!-- تعديل العناوين مع تنسيق أفضل -->
        <xpath expr="//table[hasclass('table')]//thead/tr" position="replace">
            <tr class="text-center">
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">#</th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Ticket</div>
                    <div style="font-weight: bold; font-size: 11px;">رقم التذكرة</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">First Name</div>
                    <div style="font-weight: bold; font-size: 11px;">الاسم الأول</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Surname</div>
                    <div style="font-weight: bold; font-size: 11px;">اسم العائلة</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Birth Date</div>
                    <div style="font-weight: bold; font-size: 11px;">تاريخ الميلاد</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">NTLY</div>
                    <div style="font-weight: bold; font-size: 11px;">الجنسية</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Pass S/N</div>
                    <div style="font-weight: bold; font-size: 11px;">رقم الجواز</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Issue Date</div>
                    <div style="font-weight: bold; font-size: 11px;">تاريخ الإصدار</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Ex Date</div>
                    <div style="font-weight: bold; font-size: 11px;">تاريخ الانتهاء</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Age</div>
                    <div style="font-weight: bold; font-size: 11px;">العمر</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Kind</div>
                    <div style="font-weight: bold; font-size: 11px;">النوع</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Class</div>
                    <div style="font-weight: bold; font-size: 11px;">الدرجة</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Pack</div>
                    <div style="font-weight: bold; font-size: 11px;">الحقائب</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Tag</div>
                    <div style="font-weight: bold; font-size: 11px;">الوسم</div>
                </th>
                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000;">
                    <div style="font-weight: bold;">Lugg</div>
                    <div style="font-weight: bold; font-size: 11px;">الأمتعة</div>
                </th>
            </tr>
        </xpath>

        <!-- عرض البيانات -->
        <xpath expr="//tr[@t-foreach='attendees']" position="replace">
            <t t-set="count" t-value="1"/>
            <tr t-foreach="attendees" t-as="attendee">
                <t t-set="pax_name" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'First Name')"/>
                <t t-set="father_name" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Surname')"/>
                <t t-set="birth_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Date of Birth')"/>
                <t t-set="ntly" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Nationality')"/>
                <t t-set="pass_sn" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Passport S/N')"/>
                <t t-set="issue_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Pass Issue Date')"/>
                <t t-set="exp_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Pass Expiry Date')"/>
                <t t-set="age" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Age')"/>
                <t t-set="kind" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Kind')"/>
                <t t-set="class" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Class')"/>
                <t t-set="pack" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Packing')"/>
                <t t-set="tag" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Tag')"/>
                <t t-set="lugg" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Lugg')"/>

                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="count"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="attendee.barcode"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="pax_name.value_text_box"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="father_name.value_text_box"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="birth_date.value_text_box"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="ntly.value_answer_id.name"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="pass_sn.value_text_box"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="issue_date.value_text_box"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="exp_date.value_text_box"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="age.value_answer_id.name"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="kind.value_answer_id.name"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="class.value_answer_id.name"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="pack.value_text_box"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="tag.value_text_box"/></td>
                <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="lugg.value_text_box"/></td>
                <t t-set="count" t-value="count+1"/>
            </tr>
        </xpath>

        <!-- إضافة تقرير الجنسيات في نهاية التقرير -->
        <xpath expr="//table[hasclass('table')]" position="after">
            <!-- حساب إحصائيات الجنسيات -->
            <t t-set="all_nationalities" t-value="attendees.mapped('registration_answer_ids').filtered(lambda x: x.question_id.title == 'Nationality' and x.value_answer_id)"/>
            <t t-set="unique_nationalities" t-value="list(set(all_nationalities.mapped('value_answer_id.name')))"/>

            <!-- عرض تقرير الجنسيات -->
            <div class="row mt-4">
                <div class="col-12">
                    <h4 class="text-center" style="margin-bottom: 15px; font-weight: bold;">
                        <div>Nationality Summary</div>
                        <div style="font-size: 14px;">ملخص الجنسيات</div>
                    </h4>
                    <table class="table table-bordered" style="margin: 0 auto; width: 50%;">
                        <thead>
                            <tr class="text-center">
                                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000; background-color: #f8f9fa;">
                                    <div style="font-weight: bold;">Nationality</div>
                                    <div style="font-weight: bold; font-size: 11px;">الجنسية</div>
                                </th>
                                <th style="vertical-align: middle; padding: 8px; font-size: 12px; border: 1px solid #000; background-color: #f8f9fa;">
                                    <div style="font-weight: bold;">Count</div>
                                    <div style="font-weight: bold; font-size: 11px;">العدد</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="unique_nationalities" t-as="nationality">
                                <t t-set="nat_count" t-value="len(all_nationalities.filtered(lambda x: x.value_answer_id.name == nationality))"/>
                                <tr>
                                    <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="nationality"/></td>
                                    <td style="text-align: center; padding: 6px; font-size: 11px; border: 1px solid #000;"><t t-out="nat_count"/></td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                </div>
            </div>
        </xpath>

    </template>
</odoo>

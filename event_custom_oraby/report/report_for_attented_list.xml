<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="inherit_event_attendee_list" inherit_id="event.attendee_list">

        <!-- التاريخ والوقت -->
        <xpath expr="//div[hasclass('row')][1]" position="after">
            <div class="row" style="text-align: right; margin-bottom: 20px;">
                <span><b>Date:</b> <t t-out="event.date_begin.strftime('%Y-%m-%d')"/></span>
                <span style="margin-left: 20px;"><b>Time:</b> <t t-out="event.date_begin.strftime('%H:%M')"/></span>
            </div>
        </xpath>

        <!-- رقم الرحلة بدلاً من Vessel -->
        <xpath expr="//div[hasclass('col-4')][1]" position="replace">
            <div class="col-4" style="margin-bottom: 20px;">
                <span><b>Flight No / رقم الرحلة:</b> TP<t t-out="event.name or '031'"/></span><br/>
                <span><b>From / من:</b> <t t-out="event.from_location_id.name"/></span><br/>
                <span><b>To / إلى:</b> <t t-out="event.to_location_id.name"/></span>
            </div>
        </xpath>

        <!-- تعديل العناوين -->
        <xpath expr="//table[hasclass('table')][1]//thead/tr" position="replace">
            <tr class="text-start">
                <th>#</th>
                <th>رقم التذكرة / Ticket</th>
                <th>الاسم الأول / First Name</th>
                <th>اسم العائلة / Surname</th>
                <th>تاريخ الميلاد / Birth Date</th>
                <th>الجنسية / Nationality</th>
                <th>رقم الجواز / Passport No</th>
                <th>تاريخ الإصدار / Issue Date</th>
                <th>تاريخ الانتهاء / Expiry Date</th>
                <th>العمر / Age</th>
                <th>النوع / Kind</th>
                <th>الدرجة / Class</th>
                <th>الشحنة / Pack</th>
                <th>الوسم / Tag</th>
                <th>الأمتعة / Luggage</th>
            </tr>
        </xpath>

        <!-- عرض البيانات -->
        <xpath expr="//tr[@t-foreach='attendees']" position="replace">
            <t t-set="count" t-value="1"/>
            <tr t-foreach="attendees" t-as="attendee">
                <t t-set="pax_name" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'First Name')"/>
                <t t-set="father_name" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Surname')"/>
                <t t-set="birth_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Date of Birth')"/>
                <t t-set="ntly" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Nationality')"/>
                <t t-set="pass_sn" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Passport S/N')"/>
                <t t-set="issue_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Pass Issue Date')"/>
                <t t-set="exp_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Pass Expiry Date')"/>
                <t t-set="age" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Age')"/>
                <t t-set="kind" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Kind')"/>
                <t t-set="class" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Class')"/>
                <t t-set="pack" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Packing')"/>
                <t t-set="tag" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Tag')"/>
                <t t-set="lugg" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Lugg')"/>

                <td><t t-out="count"/></td>
                <td><t t-out="attendee.barcode"/></td>
                <td><t t-out="pax_name.value_text_box"/></td>
                <td><t t-out="father_name.value_text_box"/></td>
                <td><t t-out="birth_date.value_text_box"/></td>
                <td><t t-out="ntly.value_answer_id.name"/></td>
                <td><t t-out="pass_sn.value_text_box"/></td>
                <td><t t-out="issue_date.value_text_box"/></td>
                <td><t t-out="exp_date.value_text_box"/></td>
                <td><t t-out="age.value_answer_id.name"/></td>
                <td><t t-out="kind.value_answer_id.name"/></td>
                <td><t t-out="class.value_answer_id.name"/></td>
                <td><t t-out="pack.value_text_box"/></td>
                <td><t t-out="tag.value_text_box"/></td>
                <td><t t-out="lugg.value_text_box"/></td>
                <t t-set="count" t-value="count+1"/>
            </tr>
        </xpath>

    </template>
</odoo>

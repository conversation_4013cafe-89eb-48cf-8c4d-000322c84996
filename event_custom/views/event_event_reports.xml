<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="inherit_event_attendee_list" inherit_id="event.attendee_list">

        <!-- Add Date and Time at the top right -->
        <xpath expr="//div[hasclass('row')][1]" position="after">
            <div class="row" style="text-align: right; margin-bottom: 20px;">
                <span><b>Date: 07/18/2025</b></span>&nbsp;&nbsp;
                <span><b>Time: 11:30</b></span>
            </div>
        </xpath>

        <!-- Update Vessel with trip number and add Date/Time on the opposite side -->
        <xpath expr="//div[hasclass('col-4')][1]" position="replace">
            <div class="col-4" style="margin-bottom: 20px;">
                <span><b>Vessel: TP<t t-out="event.name or 'TP031'"/></b></span><br/>
                <span><b>Voyage From:</b> <t t-out="event.from_location_id.name"/></span><br/>
                <span><b>To:</b> <t t-out="event.to_location_id.name"/></span>
            </div>
            <div class="col-4" style="text-align: right; margin-bottom: 20px;">
                <!-- Date and Time already added at the top, so this can be left empty or removed -->
            </div>
        </xpath>

        <!-- Update table headers with Arabic and English -->
        <xpath expr="//table[hasclass('table')][1]//thead/tr" position="replace">
            <tr class="text-start">
                <th>رقم التذكرة / Ticket</th>
                <th>الاسم الأول / First Name</th>
                <th>اسم الأب / Surname</th>
                <th>تاريخ الميلاد / Birth Date</th>
                <th>الجنسية / NTLY</th>
                <th>رقم الجواز / Pass S/N</th>
                <th>تاريخ الإصدار / Issue Date</th>
                <th>تاريخ الانتهاء / Ex Date</th>
                <th>العمر / Age</th>
                <th>النوع / Kind</th>
                <th>الفئة / Class</th>
                <th>التعبئة / Pack</th>
                <th>الوسم / Tag</th>
                <th>الحقائب / Lugg</th>
            </tr>
        </xpath>

        <!-- Update Pax Name to First Name and Father Name to Surname in the table body -->
        <xpath expr="//tr[@t-foreach='attendees']" position="replace">
            <tr t-foreach="attendees" t-as="attendee">
                <t t-set="pax_name" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'First Name')"/>
                <t t-set="father_name" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Surname')"/>
                <t t-set="birth_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Date of Birth')"/>
                <t t-set="ntly" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Nationality')"/>
                <t t-set="pass_sn" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Passport S/N')"/>
                <t t-set="issue_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Pass Issue Date')"/>
                <t t-set="exp_date" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Pass Expiry Date')"/>
                <t t-set="age" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Age')"/>
                <t t-set="kind" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Kind')"/>
                <t t-set="class" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Class')"/>
                <t t-set="pack" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Packing')"/>
                <t t-set="tag" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Tag')"/>
                <t t-set="lugg" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Lugg')"/>

                <td><t t-out="count"/></td>
                <td><t t-out="attendee.barcode"/></td>
                <td><t t-out="pax_name.value_text_box"/></td>
                <td><t t-out="father_name.value_text_box"/></td>
                <td><t t-out="birth_date.value_text_box"/></td>
                <td><t t-out="ntly.value_answer_id.name"/></td>
                <td><t t-out="pass_sn.value_text_box"/></td>
                <td><t t-out="issue_date.value_text_box"/></td>
                <td><t t-out="exp_date.value_text_box"/></td>
                <td><t t-out="age.value_answer_id.name"/></td>
                <td><t t-out="kind.value_answer_id.name"/></td>
                <td><t t-out="class.value_answer_id.name"/></td>
                <td><t t-out="pack.value_text_box"/></td>
                <td><t t-out="tag.value_text_box"/></td>
                <td><t t-out="lugg.value_text_box"/></td>
                <t t-set="count" t-value="count+1"/>
            </tr>
        </xpath>

        <!-- Add nationality summary at the bottom of the report -->
        <xpath expr="//table[hasclass('table')][1]" position="after">
            <div style="margin-top: 20px;"></div> <!-- Spacing between tables -->
            <table class="table mt-3">
                <thead>
                    <tr>
                        <th>Nationality / الجنسية</th>
                        <th>Count / العدد</th>
                    </tr>
                </thead>
                <tbody>
                    <t t-set="nationality_counts" t-value="{}"/>
                    <t t-foreach="attendees" t-as="attendee">
                        <t t-set="ntly" t-value="attendee.registration_answer_ids.filtered(lambda x: x.question_id.title == 'Nationality')"/>
                        <t t-if="ntly">
                            <t t-set="nationality" t-value="ntly.value_answer_id.name"/>
                            <t t-set="nationality_counts" t-value="nationality_counts.update({nationality: nationality_counts.get(nationality, 0) + 1}) if nationality else nationality_counts"/>
                        </t>
                    </t>
                    <t t-foreach="nationality_counts" t-as="nat_count">
                        <tr>
                            <td><t t-out="nat_count[0]"/></td>
                            <td><t t-out="nat_count[1]"/></td>
                        </tr>
                    </t>
                </tbody>
            </table>
        </xpath>

    </template>
</odoo>